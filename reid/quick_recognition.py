#!/usr/bin/env python3
"""
快速猫咪识别脚本
使用优化后的模型进行快速识别
"""

import torch
import torch.nn.functional as F
import json
import numpy as np
from pathlib import Path
import logging
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import argparse

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_path: str, device):
    """加载模型"""
    from feature_based_cat_recognition import FeatureExtractorModel
    
    checkpoint = torch.load(model_path, map_location=device)
    model = FeatureExtractorModel(feature_dim=2048)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.to(device)
    
    return model

class QuickRecognizer:
    """快速识别器"""
    
    def __init__(self, model_path: str = "feature_extractor_model_half.pth", 
                 threshold: float = 0.94):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.threshold = threshold
        
        # 数据变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 加载模型
        logger.info(f"加载模型: {model_path}")
        self.model = load_model(model_path, self.device)
        
        # 构建参考特征
        self.reference_features = self._build_reference_features()
        
        logger.info("快速识别器初始化完成!")
    
    def _build_reference_features(self) -> dict:
        """构建参考特征库"""
        annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
        images_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
        
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        reference_features = defaultdict(list)
        
        # 按类别收集图片
        class_images = defaultdict(list)
        for img_path, annotation_data in annotations.items():
            if isinstance(annotation_data, dict):
                label = annotation_data.get('category', '小白')
            else:
                label = annotation_data
            
            if label in ['小白', '小花', '小黑']:
                full_path = images_dir / Path(img_path).name
                if full_path.exists():
                    class_images[label].append(str(full_path))
        
        # 为每个类别提取特征
        for class_name, image_paths in class_images.items():
            logger.info(f"为类别 {class_name} 提取参考特征...")
            
            # 使用100个样本
            selected_paths = image_paths[:100] if len(image_paths) >= 100 else image_paths * (100 // len(image_paths) + 1)
            selected_paths = selected_paths[:100]
            
            for img_path in selected_paths:
                try:
                    features = self._extract_features(img_path)
                    reference_features[class_name].append(features)
                except Exception as e:
                    logger.warning(f"无法处理图片 {img_path}: {e}")
        
        # 计算增强特征
        enhanced_features = {}
        for class_name, features_list in reference_features.items():
            if features_list:
                stacked_features = torch.stack(features_list)
                mean_features = torch.mean(stacked_features, dim=0)
                median_features = torch.median(stacked_features, dim=0)[0]
                enhanced_features[class_name] = 0.7 * mean_features + 0.3 * median_features
                logger.info(f"类别 {class_name}: {len(features_list)} 个样本")
        
        return enhanced_features
    
    def _extract_features(self, image_path: str) -> torch.Tensor:
        """提取单张图片的特征"""
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            features = self.model(image_tensor)
        
        return features.squeeze(0)
    
    def recognize(self, image_path: str) -> dict:
        """识别单张图片"""
        try:
            query_features = self._extract_features(image_path)
            
            similarities = {}
            for class_name, ref_features in self.reference_features.items():
                similarity = F.cosine_similarity(query_features.unsqueeze(0), ref_features.unsqueeze(0)).item()
                similarities[class_name] = similarity
            
            # 找到最高相似度
            best_class = max(similarities, key=similarities.get)
            best_similarity = similarities[best_class]
            
            # 应用阈值
            if best_similarity >= self.threshold:
                result = {
                    'predicted_class': best_class,
                    'confidence': best_similarity,
                    'all_similarities': similarities,
                    'status': 'success'
                }
            else:
                result = {
                    'predicted_class': None,
                    'confidence': best_similarity,
                    'all_similarities': similarities,
                    'status': 'rejected',
                    'reason': f'置信度 {best_similarity:.3f} 低于阈值 {self.threshold}'
                }
            
            return result
            
        except Exception as e:
            return {
                'predicted_class': None,
                'confidence': 0.0,
                'all_similarities': {},
                'status': 'error',
                'reason': str(e)
            }
    
    def batch_recognize(self, image_paths: list) -> list:
        """批量识别"""
        results = []
        for img_path in image_paths:
            result = self.recognize(img_path)
            result['image_path'] = img_path
            results.append(result)
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='快速猫咪识别')
    parser.add_argument('--image', '-i', type=str, help='单张图片路径')
    parser.add_argument('--batch', '-b', type=str, help='图片目录路径')
    parser.add_argument('--model', '-m', type=str, default='feature_extractor_model_half.pth', help='模型路径')
    parser.add_argument('--threshold', '-t', type=float, default=0.94, help='识别阈值')
    
    args = parser.parse_args()
    
    # 初始化识别器
    recognizer = QuickRecognizer(model_path=args.model, threshold=args.threshold)
    
    if args.image:
        # 单张图片识别
        logger.info(f"识别图片: {args.image}")
        result = recognizer.recognize(args.image)
        
        print(f"\n🎯 识别结果:")
        print(f"图片: {args.image}")
        if result['status'] == 'success':
            print(f"识别结果: {result['predicted_class']}")
            print(f"置信度: {result['confidence']:.4f}")
            print(f"所有相似度: {result['all_similarities']}")
        else:
            print(f"识别失败: {result['reason']}")
    
    elif args.batch:
        # 批量识别
        image_dir = Path(args.batch)
        image_paths = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_paths.extend(image_dir.glob(ext))
        
        logger.info(f"批量识别 {len(image_paths)} 张图片...")
        results = recognizer.batch_recognize([str(p) for p in image_paths])
        
        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        rejected_count = sum(1 for r in results if r['status'] == 'rejected')
        error_count = sum(1 for r in results if r['status'] == 'error')
        
        print(f"\n📊 批量识别结果:")
        print(f"总图片数: {len(results)}")
        print(f"成功识别: {success_count}")
        print(f"置信度不足: {rejected_count}")
        print(f"识别错误: {error_count}")
        
        # 按类别统计
        class_counts = defaultdict(int)
        for result in results:
            if result['status'] == 'success':
                class_counts[result['predicted_class']] += 1
        
        print(f"\n🐱 各类别统计:")
        for class_name, count in class_counts.items():
            print(f"{class_name}: {count} 张")
        
        # 保存详细结果
        output_file = f"batch_recognition_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n详细结果已保存到: {output_file}")
    
    else:
        print("请指定 --image 或 --batch 参数")
        print("使用示例:")
        print("  python quick_recognition.py --image /path/to/image.jpg")
        print("  python quick_recognition.py --batch /path/to/images/")

if __name__ == "__main__":
    main()
