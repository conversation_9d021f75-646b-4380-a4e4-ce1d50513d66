#!/usr/bin/env python3
"""
改进的红外图像训练脚本
基于错误分析结果，采用多种优化策略提升性能
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import time
import random

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, WeightedRandomSampler
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
# import albumentations as A
# from albumentations.pytorch import ToTensorV2

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedInfraredTransforms:
    """高级红外图像变换"""

    def __init__(self, image_size=224, is_training=True):
        self.image_size = image_size
        self.is_training = is_training

        if is_training:
            # 训练时的强化数据增强
            self.transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomRotation(degrees=15),
                transforms.RandomAffine(degrees=10, translate=(0.1, 0.1), scale=(0.9, 1.1)),
                transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.2, hue=0.1),
                transforms.RandomApply([transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0))], p=0.3),
                transforms.RandomApply([transforms.RandomErasing(p=0.5, scale=(0.02, 0.1))], p=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                # 添加噪声
                # transforms.Lambda(lambda x: x + torch.randn_like(x) * 0.01 if random.random() < 0.3 else x)
            ])
        else:
            # 验证时的简单变换
            self.transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

    def __call__(self, image):
        return self.transform(image)

class ImprovedInfraredDataset(Dataset):
    """改进的红外猫咪数据集"""
    
    def __init__(self, annotations_dict: Dict, images_dir: str, cat_to_id: Dict[str, int], 
                 transform=None, is_training=True, augment_factor=3):
        self.annotations = []
        self.images_dir = images_dir
        self.cat_to_id = cat_to_id
        self.transform = transform
        self.is_training = is_training
        self.augment_factor = augment_factor if is_training else 1
        
        # 处理标注数据
        for filename, annotation in annotations_dict.items():
            if annotation['category'] in cat_to_id:
                self.annotations.append({
                    'filename': filename,
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']]
                })
        
        # 计算类别权重（用于平衡采样）
        self.class_counts = {}
        for ann in self.annotations:
            cat = ann['category']
            self.class_counts[cat] = self.class_counts.get(cat, 0) + 1
        
        logger.info(f"数据集包含 {len(self.annotations)} 个样本")
        logger.info(f"类别分布: {self.class_counts}")
        
        # 为训练集创建增强样本
        if is_training and augment_factor > 1:
            original_annotations = self.annotations.copy()
            for _ in range(augment_factor - 1):
                self.annotations.extend(original_annotations)
            logger.info(f"数据增强后: {len(self.annotations)} 个样本")
    
    def __len__(self):
        return len(self.annotations)
    
    def __getitem__(self, idx):
        annotation = self.annotations[idx]
        image_path = os.path.join(self.images_dir, annotation['filename'])
        
        try:
            image = Image.open(image_path).convert('RGB')
            
            # 对红外图像进行特殊预处理
            if self.is_training:
                # 随机应用红外图像特有的增强
                if random.random() < 0.3:
                    # 增强对比度
                    enhancer = ImageEnhance.Contrast(image)
                    image = enhancer.enhance(random.uniform(1.2, 1.8))
                
                if random.random() < 0.2:
                    # 轻微模糊（模拟红外成像特点）
                    image = image.filter(ImageFilter.GaussianBlur(radius=random.uniform(0.5, 1.0)))
                
        except Exception as e:
            logger.error(f"无法加载图像 {image_path}: {e}")
            # 创建默认图像
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        if self.transform:
            image = self.transform(image)
        
        return image, annotation['label'], annotation['category']
    
    def get_class_weights(self):
        """获取类别权重用于损失函数"""
        total_samples = len(self.annotations)
        weights = []
        for cat in sorted(self.cat_to_id.keys()):
            weight = total_samples / (len(self.cat_to_id) * self.class_counts[cat])
            weights.append(weight)
        return torch.FloatTensor(weights)

class FocalLoss(nn.Module):
    """Focal Loss - 处理类别不平衡和困难样本"""
    
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        ce_loss = nn.CrossEntropyLoss(reduction='none')(inputs, targets)
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class ImprovedFeatureExtractor(nn.Module):
    """改进的特征提取器"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=3):
        super(ImprovedFeatureExtractor, self).__init__()
        self.base_model = base_model
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        
        # 添加额外的特征处理层
        self.feature_processor = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
        )
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, num_classes)
        )
    
    def forward(self, x, return_features=False):
        # 基础特征提取
        base_features = self.base_model(x)
        
        # 特征处理
        processed_features = self.feature_processor(base_features)
        
        if return_features:
            return processed_features
        
        # 分类
        logits = self.classifier(processed_features)
        return logits, processed_features

def create_improved_datasets(
    annotations_path: str,
    original_images_dir: str,
    infrared_images_dir: str,
    train_ratio: float = 0.8,
    augment_factor: int = 3
) -> Tuple[Dataset, Dataset, Dict[str, int], Dict[int, str]]:
    """创建改进的红外图像数据集"""
    
    logger.info("创建改进的红外图像数据集...")
    
    # 加载标注数据
    with open(annotations_path, 'r', encoding='utf-8') as f:
        annotations_dict = json.load(f)
    
    # 创建红外图像标注映射
    infrared_annotations = {}
    for original_filename, annotation in annotations_dict.items():
        base_name = original_filename.replace('.jpg', '')
        infrared_filename = f"{base_name}_ir.jpg"
        infrared_path = os.path.join(infrared_images_dir, infrared_filename)
        
        if os.path.exists(infrared_path):
            infrared_annotations[infrared_filename] = annotation
    
    logger.info(f"找到 {len(infrared_annotations)} 个红外图像标注")
    
    # 创建类别映射
    categories = list(set(ann['category'] for ann in infrared_annotations.values()))
    categories = [cat for cat in categories if cat != "无"]
    categories.sort()
    
    cat_to_id = {cat: idx for idx, cat in enumerate(categories)}
    id_to_cat = {idx: cat for cat, idx in cat_to_id.items()}
    
    # 分割数据（确保每个类别都有训练和验证样本）
    train_items = {}
    val_items = {}
    
    for cat in categories:
        cat_items = [(k, v) for k, v in infrared_annotations.items() if v['category'] == cat]
        random.shuffle(cat_items)
        
        split_idx = max(1, int(len(cat_items) * train_ratio))  # 确保至少有1个验证样本
        
        for k, v in cat_items[:split_idx]:
            train_items[k] = v
        for k, v in cat_items[split_idx:]:
            val_items[k] = v
    
    # 创建数据变换
    train_transform = AdvancedInfraredTransforms(224, is_training=True)
    val_transform = AdvancedInfraredTransforms(224, is_training=False)
    
    # 创建数据集
    train_dataset = ImprovedInfraredDataset(
        train_items, infrared_images_dir, cat_to_id, train_transform, 
        is_training=True, augment_factor=augment_factor
    )
    val_dataset = ImprovedInfraredDataset(
        val_items, infrared_images_dir, cat_to_id, val_transform, 
        is_training=False
    )
    
    return train_dataset, val_dataset, cat_to_id, id_to_cat

def train_improved_model(
    model: nn.Module,
    train_loader: DataLoader,
    val_loader: DataLoader,
    device: torch.device,
    num_epochs: int = 50,
    lr: float = 1e-4
) -> nn.Module:
    """训练改进的模型"""
    
    logger.info("开始训练改进的红外模型...")
    
    # 损失函数组合
    focal_loss = FocalLoss(alpha=1, gamma=2)
    triplet_loss = nn.TripletMarginLoss(margin=0.3)
    
    # 优化器和调度器
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, max_lr=lr*10, epochs=num_epochs, 
        steps_per_epoch=len(train_loader), pct_start=0.1
    )
    
    best_val_acc = 0.0
    patience = 15
    patience_counter = 0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        num_batches = 0
        
        for batch_idx, (images, labels, _) in enumerate(train_loader):
            images = images.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            logits, features = model(images)
            
            # 分类损失（使用Focal Loss）
            classification_loss = focal_loss(logits, labels)
            
            # 三元组损失（如果批次足够大）
            triplet_loss_val = 0
            if len(torch.unique(labels)) > 1 and len(features) >= 6:
                # 简单的三元组构建
                unique_labels = torch.unique(labels)
                if len(unique_labels) >= 2:
                    anchor_mask = labels == unique_labels[0]
                    positive_mask = labels == unique_labels[0]
                    negative_mask = labels == unique_labels[1]
                    
                    if anchor_mask.sum() > 0 and positive_mask.sum() > 1 and negative_mask.sum() > 0:
                        anchor_idx = torch.where(anchor_mask)[0][0]
                        positive_idx = torch.where(positive_mask)[0][-1]
                        negative_idx = torch.where(negative_mask)[0][0]
                        
                        triplet_loss_val = triplet_loss(
                            features[anchor_idx:anchor_idx+1],
                            features[positive_idx:positive_idx+1],
                            features[negative_idx:negative_idx+1]
                        )
            
            # 总损失
            total_loss = classification_loss + 0.1 * triplet_loss_val
            
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step()
            
            train_loss += total_loss.item()
            num_batches += 1
        
        # 验证阶段
        if (epoch + 1) % 5 == 0:
            val_acc = evaluate_improved_model(model, train_loader, val_loader, device)
            
            logger.info(f"Epoch {epoch+1}/{num_epochs}")
            logger.info(f"  训练损失: {train_loss/num_batches:.4f}")
            logger.info(f"  验证准确率: {val_acc:.4f}")
            logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.2e}")
            
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                logger.info(f"  新的最佳验证准确率: {best_val_acc:.4f}")
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                logger.info(f"早停：验证准确率连续{patience}轮未提升")
                break
    
    logger.info(f"训练完成! 最佳验证准确率: {best_val_acc:.4f}")
    return model

def evaluate_improved_model(model: nn.Module, train_loader: DataLoader, val_loader: DataLoader, device: torch.device) -> float:
    """评估改进的模型"""
    
    model.eval()
    
    # 提取训练集特征（参考库）
    train_features = []
    train_labels = []
    
    with torch.no_grad():
        for images, labels, _ in train_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            train_features.append(features.cpu().numpy())
            train_labels.append(labels.numpy())
    
    train_features = np.vstack(train_features)
    train_labels = np.hstack(train_labels)
    
    # 提取验证集特征
    val_features = []
    val_labels = []
    
    with torch.no_grad():
        for images, labels, _ in val_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            val_features.append(features.cpu().numpy())
            val_labels.append(labels.numpy())
    
    val_features = np.vstack(val_features)
    val_labels = np.hstack(val_labels)
    
    # 特征标准化
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    val_features_scaled = scaler.transform(val_features)
    
    # 使用KNN分类器
    knn = KNeighborsClassifier(n_neighbors=min(7, len(train_features)), metric='cosine')
    knn.fit(train_features_scaled, train_labels)
    
    # 预测
    val_pred = knn.predict(val_features_scaled)
    
    # 计算准确率
    accuracy = accuracy_score(val_labels, val_pred)
    
    return accuracy

def main():
    parser = argparse.ArgumentParser(description="改进的红外图像训练")
    parser.add_argument("--pretrained", type=str, required=True, help="预训练模型路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--output", type=str, default="./infrared_improved_model.pth", help="输出模型路径")
    parser.add_argument("--epochs", type=int, default=50, help="训练轮数")
    parser.add_argument("--lr", type=float, default=1e-4, help="学习率")
    parser.add_argument("--batch-size", type=int, default=16, help="批次大小")
    parser.add_argument("--augment-factor", type=int, default=3, help="数据增强倍数")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 设备配置
    if args.device == "auto":
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"使用设备: {device}")
    
    # 加载预训练模型
    logger.info(f"加载预训练模型: {args.pretrained}")
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    
    feature_dim = checkpoint.get('feature_dim', 2048)
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建改进的数据集
    train_dataset, val_dataset, cat_to_id, id_to_cat = create_improved_datasets(
        args.annotations, args.original_dir, args.infrared_dir, augment_factor=args.augment_factor
    )
    
    # 创建改进的模型
    model = ImprovedFeatureExtractor(base_model, feature_dim, len(cat_to_id))
    model = model.to(device)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)
    
    logger.info(f"数据加载完成:")
    logger.info(f"  训练集: {len(train_dataset)} 样本")
    logger.info(f"  验证集: {len(val_dataset)} 样本")
    logger.info(f"  类别: {list(cat_to_id.keys())}")
    
    # 开始训练
    model = train_improved_model(model, train_loader, val_loader, device, args.epochs, args.lr)
    
    # 保存模型
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'base_model_state_dict': base_model.state_dict(),
        'feature_dim': feature_dim,
        'cat_to_id': cat_to_id,
        'id_to_cat': id_to_cat,
        'improved_infrared': True,
        'original_model_path': args.pretrained
    }, output_path)
    
    logger.info(f"改进训练完成! 模型已保存: {output_path}")

if __name__ == "__main__":
    main()
