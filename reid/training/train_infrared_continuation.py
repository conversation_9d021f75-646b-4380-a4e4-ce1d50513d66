#!/usr/bin/env python3
"""
红外图像续训脚本
基于现有的猫咪识别模型，使用红外图像数据进行续训
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, Tuple

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import yaml

# 添加项目路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from models.cat_reid_model import CatReidModel
from datasets.cat_dataset import create_data_loaders
from utils.trainer import CatReidTrainer
from utils.config import load_config
from utils.logger import setup_logger

logger = setup_logger(__name__)

def create_infrared_data_loaders(
    annotations_path: str,
    original_images_dir: str,
    infrared_images_dir: str,
    batch_size: int = 32,
    **kwargs
) -> <PERSON><PERSON>[DataLoader, DataLoa<PERSON>, DataLoa<PERSON>, Dict[str, int], Dict[int, str]]:
    """
    创建红外图像数据加载器
    
    Args:
        annotations_path: 标注文件路径
        original_images_dir: 原始图像目录（用于获取标注）
        infrared_images_dir: 红外图像目录
        batch_size: 批次大小
        **kwargs: 其他参数
    
    Returns:
        数据加载器和类别映射
    """
    logger.info("创建红外图像数据加载器...")
    
    # 加载标注数据
    with open(annotations_path, 'r', encoding='utf-8') as f:
        annotations_dict = json.load(f)
    
    # 创建红外图像标注映射
    infrared_annotations = {}
    missing_files = []
    
    for original_filename, annotation in annotations_dict.items():
        # 将原始文件名转换为红外文件名
        # 例如: 2025-01-23_20-46-05_hls.jpg -> 2025-01-23_20-46-05_hls_ir.jpg
        base_name = original_filename.replace('.jpg', '')
        infrared_filename = f"{base_name}_ir.jpg"
        
        # 检查红外图像是否存在
        infrared_path = os.path.join(infrared_images_dir, infrared_filename)
        if os.path.exists(infrared_path):
            infrared_annotations[infrared_filename] = annotation
        else:
            missing_files.append(infrared_filename)
    
    logger.info(f"找到 {len(infrared_annotations)} 个红外图像标注")
    if missing_files:
        logger.warning(f"缺失 {len(missing_files)} 个红外图像文件")
    
    # 保存临时标注文件
    temp_annotations_path = "/tmp/infrared_annotations.json"
    with open(temp_annotations_path, 'w', encoding='utf-8') as f:
        json.dump(infrared_annotations, f, ensure_ascii=False, indent=2)
    
    # 使用现有的数据加载器创建函数
    return create_data_loaders(
        annotations_path=temp_annotations_path,
        images_dir=infrared_images_dir,
        batch_size=batch_size,
        **kwargs
    )

def load_pretrained_model(model_path: str, device: torch.device) -> CatReidModel:
    """
    加载预训练模型
    
    Args:
        model_path: 模型文件路径
        device: 设备
    
    Returns:
        加载的模型
    """
    logger.info(f"加载预训练模型: {model_path}")
    
    # 加载检查点
    try:
        checkpoint = torch.load(model_path, map_location=device, weights_only=True)
    except:
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # 获取模型配置
    if 'model_config' in checkpoint:
        model_config = checkpoint['model_config']
        num_classes = model_config['num_classes']
        feature_dim = model_config['feature_dim']
    else:
        # 默认配置
        num_classes = 3
        feature_dim = 768
    
    # 创建模型
    model = CatReidModel(num_classes=num_classes, feature_dim=feature_dim)
    
    # 加载权重
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    logger.info(f"成功加载模型: {num_classes} 类别, {feature_dim} 特征维度")
    
    return model, checkpoint

def main():
    parser = argparse.ArgumentParser(description="红外图像续训")
    parser.add_argument("--config", type=str, required=True, help="配置文件路径")
    parser.add_argument("--pretrained", type=str, required=True, help="预训练模型路径")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--output-dir", type=str, default="./infrared_models", help="输出目录")
    parser.add_argument("--epochs", type=int, default=50, help="续训轮数")
    parser.add_argument("--lr", type=float, default=1e-5, help="学习率")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 设备配置
    if args.device == "auto":
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"使用设备: {device}")
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载预训练模型
    model, original_checkpoint = load_pretrained_model(args.pretrained, device)
    
    # 创建红外数据加载器
    train_loader, val_loader, test_loader, cat_to_id, id_to_cat = create_infrared_data_loaders(
        annotations_path=config['data']['annotations_path'],
        original_images_dir=config['data']['images_dir'],
        infrared_images_dir=args.infrared_dir,
        batch_size=config['training']['batch_size'],
        train_ratio=config['data']['train_ratio'],
        image_size=config['data']['image_size'],
        exclude_categories=config['data']['exclude_categories']
    )
    
    logger.info(f"数据加载完成:")
    logger.info(f"  训练集: {len(train_loader.dataset)} 样本")
    logger.info(f"  验证集: {len(val_loader.dataset)} 样本")
    logger.info(f"  测试集: {len(test_loader.dataset)} 样本")
    logger.info(f"  类别: {list(cat_to_id.keys())}")
    
    # 配置优化器（使用较小的学习率进行续训）
    optimizer = optim.AdamW(
        model.parameters(),
        lr=args.lr,
        weight_decay=config['training']['optimizer']['weight_decay']
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=args.epochs, eta_min=1e-7
    )
    
    # 创建训练器
    trainer = CatReidTrainer(
        model=model,
        optimizer=optimizer,
        scheduler=scheduler,
        device=device,
        save_dir=str(output_dir),
        config=config,
        use_wandb=False  # 续训时不使用wandb
    )
    
    logger.info("开始红外图像续训...")
    
    try:
        # 开始续训
        trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            cat_to_id=cat_to_id,
            id_to_cat=id_to_cat,
            num_epochs=args.epochs
        )
        
        # 保存续训后的模型
        final_model_path = output_dir / "infrared_continued_model.pth"
        
        # 保存完整的检查点
        torch.save({
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
            'model_config': {
                'num_classes': len(cat_to_id),
                'feature_dim': model.feature_dim
            },
            'cat_to_id': cat_to_id,
            'id_to_cat': id_to_cat,
            'training_config': config,
            'original_model_path': args.pretrained,
            'infrared_continued': True,
            'best_val_acc': trainer.best_val_acc
        }, final_model_path)
        
        logger.info(f"续训完成! 模型已保存: {final_model_path}")
        logger.info(f"最佳验证准确率: {trainer.best_val_acc:.4f}")
        
    except KeyboardInterrupt:
        logger.info("续训被用户中断")
        # 保存中断时的模型
        interrupted_path = output_dir / "infrared_interrupted_model.pth"
        torch.save({
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'model_config': {
                'num_classes': len(cat_to_id),
                'feature_dim': model.feature_dim
            },
            'cat_to_id': cat_to_id,
            'id_to_cat': id_to_cat,
            'interrupted': True
        }, interrupted_path)
        logger.info(f"已保存中断时的模型: {interrupted_path}")

if __name__ == "__main__":
    main()
