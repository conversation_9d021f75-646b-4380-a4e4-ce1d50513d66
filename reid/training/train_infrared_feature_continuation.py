#!/usr/bin/env python3
"""
基于特征提取模型的红外图像续训脚本
使用现有的FeatureExtractorModel进行红外图像续训
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, Tuple, List
import time

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score, f1_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfraredCatDataset(Dataset):
    """红外猫咪数据集"""
    
    def __init__(self, annotations_dict: Dict, images_dir: str, cat_to_id: Dict[str, int], transform=None):
        self.annotations = []
        self.images_dir = images_dir
        self.cat_to_id = cat_to_id
        self.transform = transform
        
        # 处理标注数据
        for filename, annotation in annotations_dict.items():
            if annotation['category'] in cat_to_id:
                self.annotations.append({
                    'filename': filename,
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']]
                })
        
        logger.info(f"数据集包含 {len(self.annotations)} 个样本")
    
    def __len__(self):
        return len(self.annotations)
    
    def __getitem__(self, idx):
        annotation = self.annotations[idx]
        image_path = os.path.join(self.images_dir, annotation['filename'])
        
        try:
            image = Image.open(image_path).convert('RGB')
        except Exception as e:
            logger.error(f"无法加载图像 {image_path}: {e}")
            # 创建默认黑色图像
            image = Image.new('RGB', (224, 224), color=(0, 0, 0))
        
        if self.transform:
            image = self.transform(image)
        
        return image, annotation['label'], annotation['category']

def create_infrared_datasets(
    annotations_path: str,
    original_images_dir: str,
    infrared_images_dir: str,
    train_ratio: float = 0.8
) -> Tuple[Dataset, Dataset, Dict[str, int], Dict[int, str]]:
    """创建红外图像数据集"""
    
    logger.info("创建红外图像数据集...")
    
    # 加载标注数据
    with open(annotations_path, 'r', encoding='utf-8') as f:
        annotations_dict = json.load(f)
    
    # 创建红外图像标注映射
    infrared_annotations = {}
    missing_files = []
    
    for original_filename, annotation in annotations_dict.items():
        # 转换文件名
        base_name = original_filename.replace('.jpg', '')
        infrared_filename = f"{base_name}_ir.jpg"
        
        # 检查红外图像是否存在
        infrared_path = os.path.join(infrared_images_dir, infrared_filename)
        if os.path.exists(infrared_path):
            infrared_annotations[infrared_filename] = annotation
        else:
            missing_files.append(infrared_filename)
    
    logger.info(f"找到 {len(infrared_annotations)} 个红外图像标注")
    if missing_files:
        logger.warning(f"缺失 {len(missing_files)} 个红外图像文件")
    
    # 创建类别映射
    categories = list(set(ann['category'] for ann in infrared_annotations.values()))
    categories = [cat for cat in categories if cat != "无"]  # 排除"无"类别
    categories.sort()
    
    cat_to_id = {cat: idx for idx, cat in enumerate(categories)}
    id_to_cat = {idx: cat for cat, idx in cat_to_id.items()}
    
    logger.info(f"检测到猫个体: {categories}")
    
    # 分割数据
    all_items = list(infrared_annotations.items())
    np.random.shuffle(all_items)
    
    split_idx = int(len(all_items) * train_ratio)
    train_items = dict(all_items[:split_idx])
    val_items = dict(all_items[split_idx:])
    
    # 创建数据变换
    train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(0.3),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    train_dataset = InfraredCatDataset(train_items, infrared_images_dir, cat_to_id, train_transform)
    val_dataset = InfraredCatDataset(val_items, infrared_images_dir, cat_to_id, val_transform)
    
    return train_dataset, val_dataset, cat_to_id, id_to_cat

def extract_features(model: nn.Module, dataloader: DataLoader, device: torch.device) -> Tuple[np.ndarray, np.ndarray]:
    """提取特征"""
    model.eval()
    features_list = []
    labels_list = []
    
    with torch.no_grad():
        for images, labels, _ in dataloader:
            images = images.to(device)
            features = model(images)
            
            features_list.append(features.cpu().numpy())
            labels_list.append(labels.numpy())
    
    features = np.vstack(features_list)
    labels = np.hstack(labels_list)
    
    return features, labels

def train_feature_extractor(
    model: nn.Module,
    train_loader: DataLoader,
    val_loader: DataLoader,
    device: torch.device,
    num_epochs: int = 30,
    lr: float = 1e-5
) -> nn.Module:
    """训练特征提取器"""
    
    logger.info("开始训练特征提取器...")
    
    # 使用对比学习损失
    criterion = nn.TripletMarginLoss(margin=0.2)
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=1e-7)
    
    best_val_acc = 0.0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        num_batches = 0
        
        for batch_idx, (images, labels, _) in enumerate(train_loader):
            images = images.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            # 提取特征
            features = model(images)
            
            # 构建三元组损失
            if len(torch.unique(labels)) > 1:  # 确保批次中有不同类别
                # 简单的三元组构建
                batch_size = features.size(0)
                if batch_size >= 3:
                    anchor_idx = torch.arange(0, batch_size, 3)[:batch_size//3]
                    positive_idx = torch.arange(1, batch_size, 3)[:batch_size//3]
                    negative_idx = torch.arange(2, batch_size, 3)[:batch_size//3]
                    
                    if len(anchor_idx) > 0:
                        anchor = features[anchor_idx]
                        positive = features[positive_idx]
                        negative = features[negative_idx]
                        
                        loss = criterion(anchor, positive, negative)
                        loss.backward()
                        optimizer.step()
                        
                        train_loss += loss.item()
                        num_batches += 1
        
        scheduler.step()
        
        # 验证阶段
        if (epoch + 1) % 5 == 0:
            val_acc = evaluate_model(model, train_loader, val_loader, device)
            
            logger.info(f"Epoch {epoch+1}/{num_epochs}")
            logger.info(f"  训练损失: {train_loss/max(num_batches, 1):.4f}")
            logger.info(f"  验证准确率: {val_acc:.4f}")
            logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.2e}")
            
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                logger.info(f"  新的最佳验证准确率: {best_val_acc:.4f}")
    
    logger.info(f"训练完成! 最佳验证准确率: {best_val_acc:.4f}")
    return model

def evaluate_model(model: nn.Module, train_loader: DataLoader, val_loader: DataLoader, device: torch.device) -> float:
    """评估模型"""
    
    # 提取训练集特征（用作参考库）
    train_features, train_labels = extract_features(model, train_loader, device)
    
    # 提取验证集特征
    val_features, val_labels = extract_features(model, val_loader, device)
    
    # 特征标准化
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    val_features_scaled = scaler.transform(val_features)
    
    # 使用KNN分类器
    knn = KNeighborsClassifier(n_neighbors=min(5, len(train_features)), metric='cosine')
    knn.fit(train_features_scaled, train_labels)
    
    # 预测
    val_pred = knn.predict(val_features_scaled)
    
    # 计算准确率
    accuracy = accuracy_score(val_labels, val_pred)
    
    return accuracy

def main():
    parser = argparse.ArgumentParser(description="红外图像特征提取模型续训")
    parser.add_argument("--pretrained", type=str, required=True, help="预训练模型路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--output", type=str, default="./infrared_feature_model.pth", help="输出模型路径")
    parser.add_argument("--epochs", type=int, default=30, help="训练轮数")
    parser.add_argument("--lr", type=float, default=1e-5, help="学习率")
    parser.add_argument("--batch-size", type=int, default=16, help="批次大小")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 设备配置
    if args.device == "auto":
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"使用设备: {device}")
    
    # 加载预训练模型
    logger.info(f"加载预训练模型: {args.pretrained}")
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    
    feature_dim = checkpoint.get('feature_dim', 2048)
    model = FeatureExtractorModel(feature_dim=feature_dim)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    
    logger.info(f"成功加载模型，特征维度: {feature_dim}")
    
    # 创建数据集
    train_dataset, val_dataset, cat_to_id, id_to_cat = create_infrared_datasets(
        args.annotations, args.original_dir, args.infrared_dir
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4)
    
    logger.info(f"数据加载完成:")
    logger.info(f"  训练集: {len(train_dataset)} 样本")
    logger.info(f"  验证集: {len(val_dataset)} 样本")
    logger.info(f"  类别: {list(cat_to_id.keys())}")
    
    # 开始续训
    model = train_feature_extractor(model, train_loader, val_loader, device, args.epochs, args.lr)
    
    # 保存模型
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'cat_to_id': cat_to_id,
        'id_to_cat': id_to_cat,
        'infrared_continued': True,
        'original_model_path': args.pretrained
    }, output_path)
    
    logger.info(f"续训完成! 模型已保存: {output_path}")

if __name__ == "__main__":
    main()
