#!/usr/bin/env python3
"""
红外模型错误分析脚本
分析模型预测错误的样本，找出性能瓶颈
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict, Counter

import torch
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfraredErrorAnalyzer:
    """红外模型错误分析器"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() and device == "auto" else device)
        self.model = None
        self.cat_to_id = None
        self.id_to_cat = None
        self.scaler = StandardScaler()
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.load_model()
    
    def load_model(self):
        """加载模型"""
        logger.info(f"加载红外模型: {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 获取模型配置
        feature_dim = checkpoint.get('feature_dim', 2048)
        self.cat_to_id = checkpoint.get('cat_to_id', {'小白': 0, '小花': 1, '小黑': 2})
        self.id_to_cat = checkpoint.get('id_to_cat', {0: '小白', 1: '小花', 2: '小黑'})
        
        # 创建模型
        self.model = FeatureExtractorModel(feature_dim=feature_dim)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model = self.model.to(self.device)
        self.model.eval()
        
        logger.info(f"成功加载模型，特征维度: {feature_dim}")
    
    def extract_features(self, image_path: str) -> np.ndarray:
        """提取单张图像的特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = self.model(image_tensor)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"提取特征失败 {image_path}: {e}")
            return np.zeros(2048)
    
    def analyze_errors(self, annotations_path: str, images_dir: str, num_samples: int = 500) -> Dict:
        """分析预测错误"""
        logger.info("开始错误分析...")
        
        # 加载标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 构建数据集
        infrared_data = []
        for original_filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                base_name = original_filename.replace('.jpg', '')
                infrared_filename = f"{base_name}_ir.jpg"
                infrared_path = os.path.join(images_dir, infrared_filename)
                
                if os.path.exists(infrared_path):
                    infrared_data.append({
                        'filename': infrared_filename,
                        'path': infrared_path,
                        'true_category': annotation['category'],
                        'true_label': self.cat_to_id[annotation['category']]
                    })
        
        # 随机采样
        import random
        test_data = random.sample(infrared_data, min(num_samples, len(infrared_data)))
        
        # 构建参考数据库（使用剩余数据）
        ref_data = [item for item in infrared_data if item not in test_data]
        
        logger.info("构建参考数据库...")
        ref_features = []
        ref_labels = []
        
        for item in ref_data:
            features = self.extract_features(item['path'])
            ref_features.append(features)
            ref_labels.append(item['true_label'])
        
        ref_features = np.vstack(ref_features)
        ref_labels = np.array(ref_labels)
        ref_features_scaled = self.scaler.fit_transform(ref_features)
        
        # 创建分类器
        knn = KNeighborsClassifier(n_neighbors=5, metric='cosine')
        knn.fit(ref_features_scaled, ref_labels)
        
        # 分析测试数据
        logger.info("分析测试样本...")
        
        correct_predictions = []
        error_predictions = []
        all_true_labels = []
        all_pred_labels = []
        
        for item in test_data:
            # 提取特征并预测
            features = self.extract_features(item['path'])
            features_scaled = self.scaler.transform(features.reshape(1, -1))
            
            pred_label = knn.predict(features_scaled)[0]
            pred_proba = knn.predict_proba(features_scaled)[0]
            confidence = max(pred_proba)
            
            pred_category = self.id_to_cat[pred_label]
            
            all_true_labels.append(item['true_label'])
            all_pred_labels.append(pred_label)
            
            prediction_info = {
                'filename': item['filename'],
                'true_category': item['true_category'],
                'pred_category': pred_category,
                'confidence': float(confidence)
            }
            
            if pred_category == item['true_category']:
                correct_predictions.append(prediction_info)
            else:
                error_predictions.append(prediction_info)
        
        # 统计分析
        accuracy = len(correct_predictions) / len(test_data)
        
        # 错误类型分析
        error_types = defaultdict(list)
        for error in error_predictions:
            error_key = f"{error['true_category']} -> {error['pred_category']}"
            error_types[error_key].append(error)
        
        # 置信度分析
        correct_confidences = [pred['confidence'] for pred in correct_predictions]
        error_confidences = [pred['confidence'] for pred in error_predictions]
        
        # 按类别分析
        category_analysis = {}
        for cat in self.cat_to_id.keys():
            cat_correct = [p for p in correct_predictions if p['true_category'] == cat]
            cat_errors = [p for p in error_predictions if p['true_category'] == cat]
            cat_total = len(cat_correct) + len(cat_errors)
            
            if cat_total > 0:
                category_analysis[cat] = {
                    'total_samples': cat_total,
                    'correct_count': len(cat_correct),
                    'error_count': len(cat_errors),
                    'accuracy': len(cat_correct) / cat_total,
                    'avg_correct_confidence': np.mean([p['confidence'] for p in cat_correct]) if cat_correct else 0,
                    'avg_error_confidence': np.mean([p['confidence'] for p in cat_errors]) if cat_errors else 0
                }
        
        # 混淆矩阵
        cm = confusion_matrix(all_true_labels, all_pred_labels)
        
        results = {
            'overall_accuracy': accuracy,
            'total_samples': len(test_data),
            'correct_count': len(correct_predictions),
            'error_count': len(error_predictions),
            'error_types': {k: len(v) for k, v in error_types.items()},
            'category_analysis': category_analysis,
            'confidence_analysis': {
                'correct_avg': np.mean(correct_confidences) if correct_confidences else 0,
                'correct_std': np.std(correct_confidences) if correct_confidences else 0,
                'error_avg': np.mean(error_confidences) if error_confidences else 0,
                'error_std': np.std(error_confidences) if error_confidences else 0
            },
            'confusion_matrix': cm.tolist(),
            'error_samples': error_predictions[:20]  # 保存前20个错误样本
        }
        
        logger.info(f"错误分析完成:")
        logger.info(f"  总体准确率: {accuracy:.4f}")
        logger.info(f"  错误样本数: {len(error_predictions)}")
        logger.info(f"  主要错误类型: {dict(sorted(error_types.items(), key=lambda x: len(x[1]), reverse=True)[:3])}")
        
        return results
    
    def plot_confusion_matrix(self, cm: np.ndarray, output_path: str):
        """绘制混淆矩阵"""
        plt.figure(figsize=(8, 6))
        
        categories = list(self.cat_to_id.keys())
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=categories, yticklabels=categories)
        
        plt.title('红外模型混淆矩阵')
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"混淆矩阵已保存: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="红外模型错误分析")
    parser.add_argument("--model", type=str, required=True, help="模型文件路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--images-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--samples", type=int, default=500, help="分析样本数")
    parser.add_argument("--output", type=str, default="infrared_error_analysis.json", help="结果输出文件")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = InfraredErrorAnalyzer(args.model, args.device)
    
    # 执行错误分析
    results = analyzer.analyze_errors(args.annotations, args.images_dir, args.samples)
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 绘制混淆矩阵
    cm = np.array(results['confusion_matrix'])
    analyzer.plot_confusion_matrix(cm, args.output.replace('.json', '_confusion_matrix.png'))
    
    logger.info(f"错误分析完成! 结果已保存: {args.output}")
    
    # 打印详细分析
    print("\n" + "="*60)
    print("红外模型错误分析报告")
    print("="*60)
    print(f"总体准确率: {results['overall_accuracy']:.4f}")
    print(f"测试样本数: {results['total_samples']}")
    print(f"正确预测: {results['correct_count']}")
    print(f"错误预测: {results['error_count']}")
    
    print("\n按类别分析:")
    for cat, stats in results['category_analysis'].items():
        print(f"  {cat}:")
        print(f"    准确率: {stats['accuracy']:.4f}")
        print(f"    样本数: {stats['total_samples']}")
        print(f"    正确置信度: {stats['avg_correct_confidence']:.4f}")
        print(f"    错误置信度: {stats['avg_error_confidence']:.4f}")
    
    print("\n主要错误类型:")
    for error_type, count in sorted(results['error_types'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {error_type}: {count} 次")
    
    print("="*60)

if __name__ == "__main__":
    main()
