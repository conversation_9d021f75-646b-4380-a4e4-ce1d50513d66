#!/usr/bin/env python3
"""
简化但改进的红外图像训练脚本
基于错误分析，采用有效的优化策略
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import time
import random

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from PIL import Image, ImageEnhance
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedInfraredDataset(Dataset):
    """改进的红外猫咪数据集"""
    
    def __init__(self, annotations_dict: Dict, images_dir: str, cat_to_id: Dict[str, int], 
                 is_training=True, augment_factor=2):
        self.annotations = []
        self.images_dir = images_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 处理标注数据
        for filename, annotation in annotations_dict.items():
            if annotation['category'] in cat_to_id:
                self.annotations.append({
                    'filename': filename,
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']]
                })
        
        # 为训练集创建增强样本
        if is_training and augment_factor > 1:
            original_annotations = self.annotations.copy()
            for _ in range(augment_factor - 1):
                self.annotations.extend(original_annotations)
        
        # 数据变换
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomRotation(degrees=10),
                transforms.ColorJitter(brightness=0.2, contrast=0.3, saturation=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        logger.info(f"数据集包含 {len(self.annotations)} 个样本")
    
    def __len__(self):
        return len(self.annotations)
    
    def __getitem__(self, idx):
        annotation = self.annotations[idx]
        image_path = os.path.join(self.images_dir, annotation['filename'])
        
        try:
            image = Image.open(image_path).convert('RGB')
            
            # 红外图像特殊处理
            if self.is_training and random.random() < 0.3:
                # 增强对比度
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(random.uniform(1.1, 1.5))
                
        except Exception as e:
            logger.error(f"无法加载图像 {image_path}: {e}")
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        image = self.transform(image)
        return image, annotation['label'], annotation['category']

class FocalLoss(nn.Module):
    """Focal Loss - 处理困难样本"""
    
    def __init__(self, alpha=1, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, inputs, targets):
        ce_loss = nn.CrossEntropyLoss(reduction='none')(inputs, targets)
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        return focal_loss.mean()

class ImprovedFeatureExtractor(nn.Module):
    """改进的特征提取器"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=3):
        super(ImprovedFeatureExtractor, self).__init__()
        self.base_model = base_model
        self.feature_dim = feature_dim
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, num_classes)
        )
    
    def forward(self, x, return_features=False):
        # 基础特征
        base_features = self.base_model(x)
        
        # 增强特征
        enhanced_features = self.feature_enhancer(base_features)
        
        if return_features:
            return enhanced_features
        
        # 分类
        logits = self.classifier(enhanced_features)
        return logits, enhanced_features

def create_improved_datasets(annotations_path, original_dir, infrared_dir, train_ratio=0.8):
    """创建改进的数据集"""
    
    # 加载标注
    with open(annotations_path, 'r', encoding='utf-8') as f:
        annotations_dict = json.load(f)
    
    # 创建红外标注映射
    infrared_annotations = {}
    for original_filename, annotation in annotations_dict.items():
        base_name = original_filename.replace('.jpg', '')
        infrared_filename = f"{base_name}_ir.jpg"
        infrared_path = os.path.join(infrared_dir, infrared_filename)
        
        if os.path.exists(infrared_path):
            infrared_annotations[infrared_filename] = annotation
    
    # 创建类别映射
    categories = list(set(ann['category'] for ann in infrared_annotations.values()))
    categories = [cat for cat in categories if cat != "无"]
    categories.sort()
    
    cat_to_id = {cat: idx for idx, cat in enumerate(categories)}
    id_to_cat = {idx: cat for cat, idx in cat_to_id.items()}
    
    # 按类别分割数据
    train_items = {}
    val_items = {}
    
    for cat in categories:
        cat_items = [(k, v) for k, v in infrared_annotations.items() if v['category'] == cat]
        random.shuffle(cat_items)
        
        split_idx = max(1, int(len(cat_items) * train_ratio))
        
        for k, v in cat_items[:split_idx]:
            train_items[k] = v
        for k, v in cat_items[split_idx:]:
            val_items[k] = v
    
    # 创建数据集
    train_dataset = ImprovedInfraredDataset(train_items, infrared_dir, cat_to_id, True, augment_factor=3)
    val_dataset = ImprovedInfraredDataset(val_items, infrared_dir, cat_to_id, False)
    
    return train_dataset, val_dataset, cat_to_id, id_to_cat

def train_improved_model(model, train_loader, val_loader, device, num_epochs=40, lr=1e-4):
    """训练改进模型"""
    
    logger.info("开始训练改进的红外模型...")
    
    # 损失函数
    focal_loss = FocalLoss(alpha=1, gamma=2)
    triplet_loss = nn.TripletMarginLoss(margin=0.3)
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=1e-6)
    
    best_val_acc = 0.0
    patience = 12
    patience_counter = 0
    
    for epoch in range(num_epochs):
        # 训练
        model.train()
        train_loss = 0.0
        num_batches = 0
        
        for images, labels, _ in train_loader:
            images = images.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            logits, features = model(images)
            
            # 分类损失
            cls_loss = focal_loss(logits, labels)
            
            # 三元组损失
            trip_loss = 0
            if len(torch.unique(labels)) > 1 and len(features) >= 3:
                # 简单三元组构建
                for i in range(0, len(features)-2, 3):
                    if i+2 < len(features):
                        trip_loss += triplet_loss(
                            features[i:i+1], features[i+1:i+2], features[i+2:i+3]
                        )
            
            total_loss = cls_loss + 0.1 * trip_loss
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += total_loss.item()
            num_batches += 1
        
        scheduler.step()
        
        # 验证
        if (epoch + 1) % 3 == 0:
            val_acc = evaluate_model(model, train_loader, val_loader, device)
            
            logger.info(f"Epoch {epoch+1}/{num_epochs}")
            logger.info(f"  训练损失: {train_loss/num_batches:.4f}")
            logger.info(f"  验证准确率: {val_acc:.4f}")
            logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.2e}")
            
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                logger.info(f"  新的最佳验证准确率: {best_val_acc:.4f}")
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                logger.info("早停")
                break
    
    logger.info(f"训练完成! 最佳验证准确率: {best_val_acc:.4f}")
    return model

def evaluate_model(model, train_loader, val_loader, device):
    """评估模型"""
    
    model.eval()
    
    # 提取训练集特征
    train_features = []
    train_labels = []
    
    with torch.no_grad():
        for images, labels, _ in train_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            train_features.append(features.cpu().numpy())
            train_labels.append(labels.numpy())
    
    train_features = np.vstack(train_features)
    train_labels = np.hstack(train_labels)
    
    # 提取验证集特征
    val_features = []
    val_labels = []
    
    with torch.no_grad():
        for images, labels, _ in val_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            val_features.append(features.cpu().numpy())
            val_labels.append(labels.numpy())
    
    val_features = np.vstack(val_features)
    val_labels = np.hstack(val_labels)
    
    # 标准化
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    val_features_scaled = scaler.transform(val_features)
    
    # KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(7, len(train_features)), metric='cosine')
    knn.fit(train_features_scaled, train_labels)
    
    val_pred = knn.predict(val_features_scaled)
    accuracy = accuracy_score(val_labels, val_pred)
    
    return accuracy

def main():
    parser = argparse.ArgumentParser(description="简化改进的红外图像训练")
    parser.add_argument("--pretrained", type=str, required=True, help="预训练模型路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--output", type=str, default="./infrared_simple_improved_model.pth", help="输出模型路径")
    parser.add_argument("--epochs", type=int, default=40, help="训练轮数")
    parser.add_argument("--lr", type=float, default=1e-4, help="学习率")
    parser.add_argument("--batch-size", type=int, default=8, help="批次大小")
    
    args = parser.parse_args()
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建数据集
    train_dataset, val_dataset, cat_to_id, id_to_cat = create_improved_datasets(
        args.annotations, args.original_dir, args.infrared_dir
    )
    
    # 创建改进模型
    model = ImprovedFeatureExtractor(base_model, feature_dim, len(cat_to_id))
    model = model.to(device)
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    logger.info(f"类别: {list(cat_to_id.keys())}")
    
    # 训练
    model = train_improved_model(model, train_loader, val_loader, device, args.epochs, args.lr)
    
    # 保存
    torch.save({
        'model_state_dict': model.state_dict(),
        'base_model_state_dict': base_model.state_dict(),
        'feature_dim': feature_dim,
        'cat_to_id': cat_to_id,
        'id_to_cat': id_to_cat,
        'simple_improved_infrared': True
    }, args.output)
    
    logger.info(f"模型已保存: {args.output}")

if __name__ == "__main__":
    main()
