{"structure_analysis": {"teacher": {"total_parameters": 99942271, "trainable_parameters": 99942271, "size_mb": 382.4565544128418, "components": {"feature_extractor": 86811002, "feature_enhancer": 8400896, "cat_classifier": 2103299, "domain_classifier": 2627074}}, "compressed": {"total_parameters": 89569661, "trainable_parameters": 89569661, "size_mb": 342.8510627746582, "components": {"feature_extractor": 86811002, "feature_compressor": 2626048, "cat_classifier": 132611}}, "compression_ratio": 1.1158049487314683, "size_reduction": 39.605491638183594}, "speed_benchmark": {"teacher": {"avg_time": 0.009871842861175538, "std_time": 0.0015492269105886027, "min_time": 0.008754968643188477, "max_time": 0.015307426452636719, "throughput": 101.29820886157421}, "compressed": {"avg_time": 0.009558982849121093, "std_time": 0.0008165766164226026, "min_time": 0.008537530899047852, "max_time": 0.01200246810913086, "throughput": 104.61364098921315}, "comparison": {"speedup": 1.0327294249809444, "throughput_ratio": 1.0327294249809444}}, "layer_profiling": {"teacher": {"feature_extraction": {"avg_time": 0.009140586853027344, "std_time": 0.0003289268125645399}, "feature_enhancement": {"avg_time": 0.0003041744232177734, "std_time": 0.00010111589006738439}, "classification": {"avg_time": 0.0001459836959838867, "std_time": 2.37779422853378e-05}, "domain_classification": {"avg_time": 0.0003214597702026367, "std_time": 3.5051414450492426e-05}}, "compressed": {"feature_extraction": {"avg_time": 0.009467267990112304, "std_time": 0.0004084765398482932}, "feature_compression": {"avg_time": 0.00030982494354248047, "std_time": 6.290509034299667e-05}, "classification": {"avg_time": 0.00015993118286132811, "std_time": 1.9999325800441112e-05}}}, "memory_analysis": {"teacher_memory_mb": 749.43017578125, "compressed_memory_mb": 749.43798828125, "memory_reduction_mb": -0.0078125, "memory_reduction_ratio": 0.9999895755217614}}