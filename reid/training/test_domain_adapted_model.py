#!/usr/bin/env python3
"""
域适应模型综合测试脚本
包括随机测试、性能测试和吞吐测试
"""

import os
import sys
import json
import argparse
import logging
import time
import random
from pathlib import Path
from typing import Dict, List, Tuple
import statistics

import torch
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel
from domain_adversarial_training import DomainAdaptationModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DomainAdaptedModelTester:
    """域适应模型测试器"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() and device == "auto" else device)
        self.model = None
        self.cat_to_id = None
        self.id_to_cat = None
        self.scaler = StandardScaler()
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.load_model()
    
    def load_model(self):
        """加载域适应模型"""
        logger.info(f"加载域适应模型: {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 获取模型配置
        feature_dim = checkpoint.get('feature_dim', 2048)
        self.cat_to_id = checkpoint.get('cat_to_id', {'小白': 0, '小花': 1, '小黑': 2})
        self.id_to_cat = checkpoint.get('id_to_cat', {0: '小白', 1: '小花', 2: '小黑'})
        
        # 创建基础模型
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['base_model_state_dict'])
        
        # 创建域适应模型
        self.model = DomainAdaptationModel(base_model, feature_dim, len(self.cat_to_id))
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model = self.model.to(self.device)
        self.model.eval()
        
        logger.info(f"成功加载域适应模型，特征维度: {feature_dim}")
        logger.info(f"支持的类别: {list(self.cat_to_id.keys())}")
    
    def extract_features(self, image_path: str) -> np.ndarray:
        """提取单张图像的特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = self.model(image_tensor, return_features=True)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"提取特征失败 {image_path}: {e}")
            return np.zeros(2048)
    
    def build_reference_database(self, annotations_path: str, original_dir: str) -> Tuple[np.ndarray, np.ndarray]:
        """构建参考数据库（使用原始图像）"""
        logger.info("构建参考数据库（原始图像）...")
        
        # 加载标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 提取原始图像特征
        features_list = []
        labels_list = []
        
        for filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                image_path = os.path.join(original_dir, filename)
                if os.path.exists(image_path):
                    features = self.extract_features(image_path)
                    features_list.append(features)
                    labels_list.append(self.cat_to_id[annotation['category']])
        
        features = np.vstack(features_list)
        labels = np.array(labels_list)
        
        # 特征标准化
        features_scaled = self.scaler.fit_transform(features)
        
        logger.info(f"参考数据库构建完成: {len(features)} 个原始图像样本")
        return features_scaled, labels
    
    def predict_single(self, image_path: str, ref_features: np.ndarray, ref_labels: np.ndarray) -> Tuple[str, float]:
        """预测单张图像"""
        # 提取特征
        features = self.extract_features(image_path)
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        
        # 使用KNN分类
        knn = KNeighborsClassifier(n_neighbors=min(7, len(ref_features)), metric='cosine')
        knn.fit(ref_features, ref_labels)
        
        # 预测
        pred_label = knn.predict(features_scaled)[0]
        pred_proba = knn.predict_proba(features_scaled)[0]
        confidence = max(pred_proba)
        
        predicted_cat = self.id_to_cat[pred_label]
        
        return predicted_cat, confidence
    
    def random_test(self, annotations_path: str, original_dir: str, infrared_dir: str, num_samples: int = 200) -> Dict:
        """随机测试（测试红外图像识别）"""
        logger.info(f"开始随机测试 ({num_samples} 个红外图像样本)...")
        
        # 构建参考数据库（原始图像）
        ref_features, ref_labels = self.build_reference_database(annotations_path, original_dir)
        
        # 加载所有标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 获取红外图像列表
        infrared_files = []
        for original_filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                base_name = original_filename.replace('.jpg', '')
                infrared_filename = f"{base_name}_ir.jpg"
                infrared_path = os.path.join(infrared_dir, infrared_filename)
                
                if os.path.exists(infrared_path):
                    infrared_files.append((infrared_filename, annotation['category']))
        
        # 随机采样
        test_samples = random.sample(infrared_files, min(num_samples, len(infrared_files)))
        
        # 进行预测
        predictions = []
        ground_truths = []
        confidences = []
        
        for filename, true_category in test_samples:
            image_path = os.path.join(infrared_dir, filename)
            pred_category, confidence = self.predict_single(image_path, ref_features, ref_labels)
            
            predictions.append(pred_category)
            ground_truths.append(true_category)
            confidences.append(confidence)
        
        # 计算指标
        accuracy = accuracy_score(ground_truths, predictions)
        
        # 按类别统计
        category_stats = {}
        for cat in self.cat_to_id.keys():
            cat_indices = [i for i, gt in enumerate(ground_truths) if gt == cat]
            if cat_indices:
                cat_preds = [predictions[i] for i in cat_indices]
                cat_acc = accuracy_score([ground_truths[i] for i in cat_indices], cat_preds)
                cat_conf = statistics.mean([confidences[i] for i in cat_indices])
                category_stats[cat] = {
                    'accuracy': cat_acc,
                    'confidence': cat_conf,
                    'count': len(cat_indices)
                }
        
        # 混淆矩阵
        cm = confusion_matrix(
            [self.cat_to_id[gt] for gt in ground_truths],
            [self.cat_to_id[pred] for pred in predictions],
            labels=list(range(len(self.cat_to_id)))
        )
        
        results = {
            'overall_accuracy': accuracy,
            'average_confidence': statistics.mean(confidences),
            'confidence_std': statistics.stdev(confidences) if len(confidences) > 1 else 0,
            'category_stats': category_stats,
            'confusion_matrix': cm.tolist(),
            'total_samples': len(test_samples)
        }
        
        logger.info(f"随机测试完成:")
        logger.info(f"  总体准确率: {accuracy:.4f}")
        logger.info(f"  平均置信度: {statistics.mean(confidences):.4f}")
        logger.info(f"  置信度标准差: {statistics.stdev(confidences) if len(confidences) > 1 else 0:.4f}")
        
        return results
    
    def throughput_test(self, infrared_dir: str, num_images: int = 100) -> Dict:
        """吞吐测试"""
        logger.info(f"开始吞吐测试 ({num_images} 张红外图像)...")
        
        # 获取测试图像
        image_files = [f for f in os.listdir(infrared_dir) if f.endswith('_ir.jpg')]
        test_images = random.sample(image_files, min(num_images, len(image_files)))
        
        # 预热
        warmup_image = os.path.join(infrared_dir, test_images[0])
        for _ in range(5):
            self.extract_features(warmup_image)
        
        # 测试吞吐
        start_time = time.time()
        
        for image_file in test_images:
            image_path = os.path.join(infrared_dir, image_file)
            self.extract_features(image_path)
        
        end_time = time.time()
        
        total_time = end_time - start_time
        throughput = len(test_images) / total_time
        avg_time_per_image = total_time / len(test_images)
        
        results = {
            'total_images': len(test_images),
            'total_time': total_time,
            'throughput': throughput,
            'avg_time_per_image': avg_time_per_image
        }
        
        logger.info(f"吞吐测试完成:")
        logger.info(f"  处理图像数: {len(test_images)}")
        logger.info(f"  总耗时: {total_time:.2f} 秒")
        logger.info(f"  吞吐量: {throughput:.2f} 图像/秒")
        logger.info(f"  平均每张: {avg_time_per_image*1000:.2f} 毫秒")
        
        return results
    
    def performance_comparison_test(self, annotations_path: str, original_dir: str, infrared_dir: str, num_samples: int = 100) -> Dict:
        """性能对比测试（原始图像 vs 红外图像）"""
        logger.info(f"开始性能对比测试 ({num_samples} 个样本)...")
        
        # 构建参考数据库
        ref_features, ref_labels = self.build_reference_database(annotations_path, original_dir)
        
        # 加载标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 获取测试样本
        test_samples = []
        for original_filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                base_name = original_filename.replace('.jpg', '')
                infrared_filename = f"{base_name}_ir.jpg"
                
                original_path = os.path.join(original_dir, original_filename)
                infrared_path = os.path.join(infrared_dir, infrared_filename)
                
                if os.path.exists(original_path) and os.path.exists(infrared_path):
                    test_samples.append({
                        'original_file': original_filename,
                        'infrared_file': infrared_filename,
                        'category': annotation['category']
                    })
        
        # 随机采样
        test_samples = random.sample(test_samples, min(num_samples, len(test_samples)))
        
        # 测试原始图像和红外图像
        original_predictions = []
        infrared_predictions = []
        ground_truths = []
        
        for sample in test_samples:
            # 原始图像预测
            original_path = os.path.join(original_dir, sample['original_file'])
            orig_pred, _ = self.predict_single(original_path, ref_features, ref_labels)
            original_predictions.append(orig_pred)
            
            # 红外图像预测
            infrared_path = os.path.join(infrared_dir, sample['infrared_file'])
            ir_pred, _ = self.predict_single(infrared_path, ref_features, ref_labels)
            infrared_predictions.append(ir_pred)
            
            ground_truths.append(sample['category'])
        
        # 计算准确率
        original_accuracy = accuracy_score(ground_truths, original_predictions)
        infrared_accuracy = accuracy_score(ground_truths, infrared_predictions)
        
        results = {
            'original_accuracy': original_accuracy,
            'infrared_accuracy': infrared_accuracy,
            'accuracy_difference': infrared_accuracy - original_accuracy,
            'total_samples': len(test_samples)
        }
        
        logger.info(f"性能对比测试完成:")
        logger.info(f"  原始图像准确率: {original_accuracy:.4f}")
        logger.info(f"  红外图像准确率: {infrared_accuracy:.4f}")
        logger.info(f"  准确率差异: {infrared_accuracy - original_accuracy:.4f}")
        
        return results

def main():
    parser = argparse.ArgumentParser(description="域适应模型综合测试")
    parser.add_argument("--model", type=str, required=True, help="域适应模型文件路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--random-samples", type=int, default=300, help="随机测试样本数")
    parser.add_argument("--throughput-samples", type=int, default=100, help="吞吐测试样本数")
    parser.add_argument("--comparison-samples", type=int, default=200, help="对比测试样本数")
    parser.add_argument("--output", type=str, default="domain_adapted_test_results.json", help="结果输出文件")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = DomainAdaptedModelTester(args.model, args.device)
    
    # 执行测试
    results = {}
    
    # 随机测试
    results['random_test'] = tester.random_test(
        args.annotations, args.original_dir, args.infrared_dir, args.random_samples
    )
    
    # 吞吐测试
    results['throughput_test'] = tester.throughput_test(args.infrared_dir, args.throughput_samples)
    
    # 性能对比测试
    results['performance_comparison'] = tester.performance_comparison_test(
        args.annotations, args.original_dir, args.infrared_dir, args.comparison_samples
    )
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"测试完成! 结果已保存: {args.output}")
    
    # 打印总结
    print("\n" + "="*60)
    print("域适应模型测试总结")
    print("="*60)
    print(f"红外图像随机测试准确率: {results['random_test']['overall_accuracy']:.4f}")
    print(f"平均置信度: {results['random_test']['average_confidence']:.4f}")
    print(f"置信度标准差: {results['random_test']['confidence_std']:.4f}")
    print(f"吞吐量: {results['throughput_test']['throughput']:.2f} 图像/秒")
    print(f"原始图像准确率: {results['performance_comparison']['original_accuracy']:.4f}")
    print(f"红外图像准确率: {results['performance_comparison']['infrared_accuracy']:.4f}")
    print(f"准确率差异: {results['performance_comparison']['accuracy_difference']:.4f}")
    print("="*60)

if __name__ == "__main__":
    main()
