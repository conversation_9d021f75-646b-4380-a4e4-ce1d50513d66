# 红外图像续训配置

# 数据配置
data:
  annotations_path: "/home/<USER>/animsi/caby_training/tagging/annotations.json"
  images_dir: "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"  # 原始图像目录（用于标注映射）
  infrared_images_dir: "/home/<USER>/animsi/caby_training/dataset/ir_thumbnails"  # 红外图像目录
  train_ratio: 0.8
  image_size: [224, 224]
  exclude_categories: ["无"]

# 模型配置（续训时会从预训练模型继承）
model:
  backbone: "hf-hub:BVRA/MegaDescriptor-T-224"
  feature_dim: 768
  dropout: 0.1
  pretrained: true
  freeze_backbone: false

# 损失函数配置（续训时使用更温和的设置）
loss:
  classification_weight: 0.005  # 续训时进一步降低分类损失权重
  triplet_weight: 1.0
  triplet_margin: 0.2  # 更小的margin
  triplet_mining: "batch_hard"
  label_smoothing: 0.05  # 更小的标签平滑

# 续训配置
training:
  num_epochs: 50  # 续训轮数较少
  batch_size: 16  # 较小的批次大小，更稳定
  num_workers: 4
  pin_memory: true
  
  # 优化器配置（续训专用）
  optimizer:
    type: "adamw"
    lr: 5e-6  # 续训使用更小的学习率
    weight_decay: 5e-5  # 更小的权重衰减
    betas: [0.9, 0.999]
    eps: 1e-8
  
  # 学习率调度器
  scheduler:
    type: "cosine"  # 简单的余弦调度
    min_lr: 1e-7
    
  # 早停配置
  early_stopping:
    patience: 15
    min_delta: 0.0005

# 验证配置
validation:
  eval_interval: 1
  save_interval: 5

# 日志配置
logging:
  log_interval: 5
  use_wandb: false  # 续训时不使用wandb
  experiment_name: "infrared_continuation"

# 设备配置
device:
  use_cuda: true
  gpu_ids: [0]

# 保存配置
save:
  save_dir: "/home/<USER>/animsi/caby_training/reid/training/infrared_experiments"
  save_best_only: false
  save_checkpoints: true

# 数据增强配置（续训时使用更温和的增强）
augmentation:
  train:
    random_horizontal_flip: 0.3  # 降低翻转概率
    random_rotation: 10  # 更小的旋转角度
    color_jitter:
      brightness: 0.1  # 更小的亮度变化
      contrast: 0.1
      saturation: 0.1
      hue: 0.05
    random_erasing: 0.1  # 降低随机擦除概率
    gaussian_blur: 0.05  # 降低模糊概率
  
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]

# 续训特定配置
continuation:
  # 是否冻结部分层
  freeze_layers: []  # 可以指定要冻结的层名称
  
  # 学习率衰减策略
  lr_decay_layers:
    backbone: 0.1  # 骨干网络使用更小的学习率
    classifier: 1.0  # 分类器使用正常学习率
  
  # 渐进式解冻
  progressive_unfreezing:
    enabled: false
    unfreeze_schedule: [10, 20, 30]  # 在这些epoch解冻更多层
